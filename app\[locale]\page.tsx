import dynamic from 'next/dynamic';
import { Locale } from '../../lib/i18n';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import HeroSection from '../../components/HeroSection';
import FeaturedProducts from '../../components/FeaturedProducts';
import CategoriesSection from '../../components/CategoriesSection';
import ServicesSection from '../../components/ServicesSection';
import PartnersSection from '../../components/PartnersSection';
import { getCategories, getFeaturedProducts } from '../../lib/server-data';

// تحميل ديناميكي للمكونات غير الضرورية للتحميل الأولي
const WhatsAppButton = dynamic(() => import('../../components/WhatsAppButton'), {
  loading: () => null
});

export default async function HomePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  // جلب البيانات في الخادم
  const [categories, featuredProducts] = await Promise.all([
    getCategories(),
    getFeaturedProducts()
  ]);

  return (
    <>
      <Navbar locale={locale} />
      <main>
        <HeroSection locale={locale} />
        <ServicesSection locale={locale} />
        <CategoriesSection locale={locale} categories={categories} />
        <FeaturedProducts locale={locale} products={featuredProducts} />
        <PartnersSection locale={locale} />
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
